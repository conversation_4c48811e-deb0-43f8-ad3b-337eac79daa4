
import { useState } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { DashboardContent } from "@/components/DashboardContent";
import { StocksPage } from "@/components/StocksPage";
import { ClientsPage } from "@/components/ClientsPage";
import { PortfoliosPage } from "@/components/PortfoliosPage";
import { ScreenerPage } from "@/components/ScreenerPage";
import { ComparePage } from "@/components/ComparePage";
import { UploadCenterPage } from "@/components/UploadCenterPage";
import { AIAssistantPage } from "@/components/AIAssistantPage";
import { SettingsPage } from "@/components/SettingsPage";
import { useAuth } from "@/contexts/AuthContext";

const Index = () => {
  const [activeTab, setActiveTab] = useState("dashboard");
  const { user, loading } = useAuth();

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const renderContent = () => {
    try {
      switch (activeTab) {
        case "dashboard":
          return <DashboardContent />;
        case "stocks":
          return <StocksPage />;
        case "clients":
          return <ClientsPage />;
        case "portfolios":
          return <PortfoliosPage />;
        case "screener":
          return <ScreenerPage />;
        case "compare":
          return <ComparePage />;
        case "upload":
          return <UploadCenterPage />;
        case "ai-assistant":
          return <AIAssistantPage />;
        case "settings":
          return <SettingsPage />;
        default:
          return <DashboardContent />;
      }
    } catch (error) {
      console.error("Error rendering content for tab:", activeTab, error);
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center text-red-600">
            <h2 className="text-xl font-bold mb-2">Error Loading Page</h2>
            <p>There was an error loading the {activeTab} page.</p>
            <p className="text-sm mt-2">Check the console for details.</p>
          </div>
        </div>
      );
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen w-full flex bg-gradient-to-br from-slate-50 to-blue-50">
        <AppSidebar activeTab={activeTab} setActiveTab={handleTabChange} />
        <div className="flex-1 flex flex-col">
          <DashboardHeader />
          <main className="flex-1 p-6">
            {renderContent()}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Index;
