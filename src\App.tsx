console.log('App.tsx: Loading App component')

const App = () => {
  console.log('App.tsx: Rendering App component')

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">Stock Air Insight</h1>
        <p className="text-gray-600 mb-8">Application is loading successfully!</p>
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <p className="text-sm text-gray-500">Basic React app is working. Now testing full features...</p>
          <button
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            onClick={() => window.location.href = '/app'}
          >
            Go to Full App
          </button>
        </div>
      </div>
    </div>
  )
}

export default App;
