import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { Upload } from '@/types/database'
import { useAuth } from '@/contexts/AuthContext'

export function useUploads() {
  const [uploads, setUploads] = useState<Upload[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()

  const fetchUploads = async () => {
    if (!user) return
    
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('uploads')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setUploads(data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const createUpload = async (uploadData: Omit<Upload, 'id' | 'created_at'>) => {
    if (!user) return null

    try {
      const { data, error } = await supabase
        .from('uploads')
        .insert([{ ...uploadData, user_id: user.id }])
        .select()
        .single()

      if (error) throw error
      setUploads(prev => [data, ...prev])
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create upload record')
      return null
    }
  }

  const updateUpload = async (id: string, updates: Partial<Upload>) => {
    try {
      const { data, error } = await supabase
        .from('uploads')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      setUploads(prev => prev.map(upload => upload.id === id ? data : upload))
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update upload')
      return null
    }
  }

  const deleteUpload = async (id: string) => {
    try {
      const { error } = await supabase
        .from('uploads')
        .delete()
        .eq('id', id)

      if (error) throw error
      setUploads(prev => prev.filter(upload => upload.id !== id))
      return true
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete upload')
      return false
    }
  }

  const uploadFile = async (
    file: File, 
    fileType: 'bhavcopy' | 'fundamentals' | 'screener_config',
    onProgress?: (progress: number) => void
  ) => {
    if (!user) {
      throw new Error('User not authenticated')
    }

    try {
      // Create upload record
      const uploadRecord = await createUpload({
        filename: file.name,
        file_type: fileType,
        file_size: file.size,
        status: 'pending',
        upload_date: new Date().toISOString(),
        records_processed: null,
        error_message: null,
        processed_at: null
      })

      if (!uploadRecord) {
        throw new Error('Failed to create upload record')
      }

      // Upload file to storage
      const filePath = `uploads/${user.id}/${fileType}/${Date.now()}_${file.name}`
      const { error: uploadError } = await supabase.storage
        .from('data-uploads')
        .upload(filePath, file, {
          onUploadProgress: (progress) => {
            const percent = (progress.loaded / progress.total) * 100
            onProgress?.(percent)
          }
        })

      if (uploadError) {
        await updateUpload(uploadRecord.id, {
          status: 'failed',
          error_message: uploadError.message
        })
        throw uploadError
      }

      // Update status to processing
      await updateUpload(uploadRecord.id, {
        status: 'processing'
      })

      return uploadRecord
    } catch (err) {
      throw err instanceof Error ? err : new Error('Upload failed')
    }
  }

  const processUpload = async (uploadId: string) => {
    try {
      // This would typically trigger a background job
      // For now, we'll simulate processing
      await updateUpload(uploadId, {
        status: 'processing'
      })

      // Simulate processing time
      setTimeout(async () => {
        await updateUpload(uploadId, {
          status: 'completed',
          processed_at: new Date().toISOString(),
          records_processed: Math.floor(Math.random() * 1000) + 100
        })
      }, 3000)

      return true
    } catch (err) {
      await updateUpload(uploadId, {
        status: 'failed',
        error_message: err instanceof Error ? err.message : 'Processing failed'
      })
      return false
    }
  }

  const getUploadsByType = (fileType: 'bhavcopy' | 'fundamentals' | 'screener_config') => {
    return uploads.filter(upload => upload.file_type === fileType)
  }

  const getRecentUploads = (limit: number = 10) => {
    return uploads.slice(0, limit)
  }

  const getUploadStats = () => {
    const total = uploads.length
    const completed = uploads.filter(u => u.status === 'completed').length
    const failed = uploads.filter(u => u.status === 'failed').length
    const processing = uploads.filter(u => u.status === 'processing').length
    const pending = uploads.filter(u => u.status === 'pending').length

    return {
      total,
      completed,
      failed,
      processing,
      pending,
      successRate: total > 0 ? (completed / total) * 100 : 0
    }
  }

  useEffect(() => {
    fetchUploads()
  }, [user])

  return {
    uploads,
    loading,
    error,
    createUpload,
    updateUpload,
    deleteUpload,
    uploadFile,
    processUpload,
    getUploadsByType,
    getRecentUploads,
    getUploadStats,
    refetch: fetchUploads
  }
}
