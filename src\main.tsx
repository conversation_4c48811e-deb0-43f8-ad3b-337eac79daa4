import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

console.log('main.tsx: Starting application')

const rootElement = document.getElementById("root")
console.log('main.tsx: Root element found:', rootElement)

if (rootElement) {
  try {
    const root = createRoot(rootElement)
    console.log('main.tsx: Root created, rendering App')
    root.render(<App />)
    console.log('main.tsx: App rendered successfully')
  } catch (error) {
    console.error('main.tsx: Error rendering app:', error)
    rootElement.innerHTML = `
      <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h1>Application Error</h1>
        <p>Failed to load the application. Check the console for details.</p>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">${error}</pre>
      </div>
    `
  }
} else {
  console.error('main.tsx: Root element not found')
}
