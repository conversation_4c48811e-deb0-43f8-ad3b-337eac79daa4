import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://roqicdoryzhtlplokury.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvcWljZG9yeXpodGxwbG9rdXJ5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMTY2MzEsImV4cCI6MjA2Njc5MjYzMX0.jZjEi7mElyxLKq2l-nVbRWa3VPH-UNHctPH2nKi8d28'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})
