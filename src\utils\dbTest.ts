import { supabase } from '@/lib/supabase'

export async function testDatabaseConnectivity() {
  const results = {
    connection: false,
    tables: {
      stocks: false,
      stock_prices: false,
      stock_fundamentals: false,
      portfolios: false,
      portfolio_holdings: false,
      clients: false,
      uploads: false,
      screener_configs: false
    },
    storage: {
      'data-uploads': false
    },
    errors: [] as string[]
  }

  try {
    // Test basic connection
    const { data, error } = await supabase.from('stocks').select('count').limit(1)
    if (error) {
      results.errors.push(`Connection error: ${error.message}`)
    } else {
      results.connection = true
    }

    // Test each table
    const tables = Object.keys(results.tables)
    for (const table of tables) {
      try {
        const { error } = await supabase.from(table).select('*').limit(1)
        if (!error) {
          results.tables[table as keyof typeof results.tables] = true
        } else {
          results.errors.push(`Table ${table}: ${error.message}`)
        }
      } catch (err) {
        results.errors.push(`Table ${table}: ${err instanceof Error ? err.message : 'Unknown error'}`)
      }
    }

    // Test storage buckets
    try {
      const { data: buckets, error } = await supabase.storage.listBuckets()
      if (error) {
        results.errors.push(`Storage error: ${error.message}`)
      } else {
        const dataUploadsBucket = buckets?.find(b => b.name === 'data-uploads')
        if (dataUploadsBucket) {
          results.storage['data-uploads'] = true
        } else {
          results.errors.push('Storage bucket "data-uploads" not found')
        }
      }
    } catch (err) {
      results.errors.push(`Storage test error: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }

  } catch (err) {
    results.errors.push(`General error: ${err instanceof Error ? err.message : 'Unknown error'}`)
  }

  return results
}

export async function createSampleData() {
  try {
    // Create sample stocks
    const sampleStocks = [
      {
        symbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        exchange: 'NSE',
        sector: 'Energy',
        industry: 'Oil & Gas',
        market_cap: 1500000000000,
        listing_date: '1977-11-29'
      },
      {
        symbol: 'TCS',
        name: 'Tata Consultancy Services Limited',
        exchange: 'NSE',
        sector: 'Technology',
        industry: 'IT Services',
        market_cap: 1200000000000,
        listing_date: '2004-08-25'
      },
      {
        symbol: 'INFY',
        name: 'Infosys Limited',
        exchange: 'NSE',
        sector: 'Technology',
        industry: 'IT Services',
        market_cap: 800000000000,
        listing_date: '1993-02-11'
      }
    ]

    const { error: stocksError } = await supabase
      .from('stocks')
      .upsert(sampleStocks, { onConflict: 'symbol' })

    if (stocksError) {
      throw new Error(`Failed to create sample stocks: ${stocksError.message}`)
    }

    // Create sample price data
    const today = new Date().toISOString().split('T')[0]
    const samplePrices = [
      {
        symbol: 'RELIANCE',
        date: today,
        open: 2500,
        high: 2550,
        low: 2480,
        close: 2530,
        volume: 1000000,
        adjusted_close: 2530
      },
      {
        symbol: 'TCS',
        date: today,
        open: 3200,
        high: 3250,
        low: 3180,
        close: 3220,
        volume: 800000,
        adjusted_close: 3220
      },
      {
        symbol: 'INFY',
        date: today,
        open: 1450,
        high: 1480,
        low: 1440,
        close: 1470,
        volume: 1200000,
        adjusted_close: 1470
      }
    ]

    const { error: pricesError } = await supabase
      .from('stock_prices')
      .upsert(samplePrices, { onConflict: 'symbol,date' })

    if (pricesError) {
      throw new Error(`Failed to create sample prices: ${pricesError.message}`)
    }

    return { success: true, message: 'Sample data created successfully' }
  } catch (error) {
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}
