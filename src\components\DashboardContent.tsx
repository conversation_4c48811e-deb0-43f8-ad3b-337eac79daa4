
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { TrendingUp, TrendingDown, Users, PieChart, DollarSign, Activity, Eye, Plus, ArrowRight, Database, CheckCircle, XCircle } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>, Cell, Pie, BarChart, Bar, Tooltip, Legend } from "recharts";
import { testDatabaseConnectivity, createSampleData } from "@/utils/dbTest";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

const mockPortfolioData = [
  { month: "Jan", value: 1000000 },
  { month: "Feb", value: 1150000 },
  { month: "Mar", value: 1080000 },
  { month: "Apr", value: 1250000 },
  { month: "May", value: 1320000 },
  { month: "Jun", value: 1450000 },
];

const topStocks = [
  { symbol: "RELIANCE", name: "Reliance Industries", price: 2456.75, change: 1.2, sector: "Energy" },
  { symbol: "TCS", name: "Tata Consultancy Services", price: 3245.80, change: -0.8, sector: "IT" },
  { symbol: "INFY", name: "Infosys Limited", price: 1567.45, change: 2.1, sector: "IT" },
  { symbol: "HDFCBANK", name: "HDFC Bank", price: 1623.90, change: 0.9, sector: "Banking" },
  { symbol: "ICICIBANK", name: "ICICI Bank", price: 1156.20, change: -1.2, sector: "Banking" },
];

const sectorData = [
  { name: "IT", value: 35, color: "#3b82f6" },
  { name: "Banking", value: 25, color: "#10b981" },
  { name: "Energy", value: 20, color: "#f59e0b" },
  { name: "Auto", value: 12, color: "#ef4444" },
  { name: "Pharma", value: 8, color: "#8b5cf6" },
];

const recentTransactions = [
  { type: "BUY", symbol: "RELIANCE", quantity: 100, price: 2456.75, date: "2024-01-15", client: "John Doe" },
  { type: "SELL", symbol: "TCS", quantity: 50, price: 3245.80, date: "2024-01-14", client: "Jane Smith" },
  { type: "BUY", symbol: "INFY", quantity: 200, price: 1567.45, date: "2024-01-13", client: "Bob Wilson" },
  { type: "SELL", symbol: "HDFC", quantity: 75, price: 1623.90, date: "2024-01-12", client: "Alice Johnson" },
];

const portfolioStats = [
  { label: "Total AUM", value: "₹14.5 Cr", change: "+12.3%", icon: DollarSign, color: "text-green-600" },
  { label: "Active Clients", value: "24", change: "+3", icon: Users, color: "text-blue-600" },
  { label: "Total Portfolios", value: "47", change: "+5", icon: PieChart, color: "text-purple-600" },
  { label: "Avg. Return", value: "18.4%", change: "+2.1%", icon: TrendingUp, color: "text-emerald-600" },
];

export function DashboardContent() {
  const [dbTestResults, setDbTestResults] = useState<any>(null);
  const [isTestingDb, setIsTestingDb] = useState(false);
  const { toast } = useToast();

  const handleDatabaseTest = async () => {
    setIsTestingDb(true);
    try {
      const results = await testDatabaseConnectivity();
      setDbTestResults(results);

      if (results.connection) {
        toast({
          title: "Database Test Complete",
          description: `Connection successful. ${Object.values(results.tables).filter(Boolean).length}/${Object.keys(results.tables).length} tables accessible.`,
        });
      } else {
        toast({
          title: "Database Test Failed",
          description: "Could not connect to database. Check console for details.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Test Error",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsTestingDb(false);
    }
  };

  const handleCreateSampleData = async () => {
    try {
      const result = await createSampleData();
      toast({
        title: result.success ? "Sample Data Created" : "Error",
        description: result.message,
        variant: result.success ? "default" : "destructive",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    }
  };
  const formatNumber = (num: number) => {
    if (num >= 10000000) return `₹${(num / 10000000).toFixed(1)}Cr`;
    if (num >= 100000) return `₹${(num / 100000).toFixed(1)}L`;
    if (num >= 1000) return `₹${(num / 1000).toFixed(1)}K`;
    return `₹${num.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
            Portfolio Dashboard
          </h1>
          <p className="text-gray-600 mt-2">Welcome back! Here's what's happening with your investments today.</p>
        </div>
        <div className="flex gap-2">
          <Button className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700">
            <Plus className="h-4 w-4 mr-2" />
            Add New Client
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {portfolioStats.map((stat, index) => (
          <Card key={index} className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <p className={`text-sm ${stat.color} mt-1`}>{stat.change}</p>
                </div>
                <div className={`p-3 rounded-full bg-gray-100 ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Portfolio Performance */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Portfolio Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={mockPortfolioData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatNumber(Number(value))} />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke="url(#gradient)" 
                    strokeWidth={3}
                    dot={{ fill: "#3b82f6", strokeWidth: 2 }}
                  />
                  <defs>
                    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#3b82f6" />
                      <stop offset="100%" stopColor="#10b981" />
                    </linearGradient>
                  </defs>
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Sector Allocation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Sector Allocation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie 
                    data={sectorData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {sectorData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Top Performing Stocks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Top Performing Stocks
              </div>
              <Button variant="ghost" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                View All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topStocks.map((stock, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold">
                      {stock.symbol.substring(0, 2)}
                    </div>
                    <div>
                      <div className="font-medium">{stock.symbol}</div>
                      <div className="text-sm text-gray-600">{stock.name}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{formatNumber(stock.price)}</div>
                    <div className={`text-sm flex items-center gap-1 ${stock.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {stock.change >= 0 ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
                      {stock.change.toFixed(1)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Transactions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Transactions
              </div>
              <Button variant="ghost" size="sm">
                <ArrowRight className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentTransactions.map((transaction, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant={transaction.type === "BUY" ? "default" : "secondary"}>
                      {transaction.type}
                    </Badge>
                    <div>
                      <div className="font-medium">{transaction.symbol}</div>
                      <div className="text-sm text-gray-600">{transaction.client}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{transaction.quantity} shares</div>
                    <div className="text-sm text-gray-600">{formatNumber(transaction.price)}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Database Status Card */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Status & Testing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-4">
            <Button
              onClick={handleDatabaseTest}
              disabled={isTestingDb}
              variant="outline"
            >
              {isTestingDb ? "Testing..." : "Test Database Connection"}
            </Button>
            <Button
              onClick={handleCreateSampleData}
              variant="outline"
            >
              Create Sample Data
            </Button>
          </div>

          {dbTestResults && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                {dbTestResults.connection ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <span className={dbTestResults.connection ? "text-green-600" : "text-red-600"}>
                  Database Connection: {dbTestResults.connection ? "Connected" : "Failed"}
                </span>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {Object.entries(dbTestResults.tables).map(([table, status]) => (
                  <div key={table} className="flex items-center gap-2 text-sm">
                    {status ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className={status ? "text-green-600" : "text-red-600"}>
                      {table}
                    </span>
                  </div>
                ))}
              </div>

              {dbTestResults.errors.length > 0 && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                  <h4 className="font-medium text-red-800 mb-2">Errors:</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {dbTestResults.errors.map((error: string, index: number) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
