import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { Portfolio, Holding, Stock, PortfolioWithHoldings } from '@/types/database'
import { useAuth } from '@/contexts/AuthContext'

export function usePortfolios(clientId?: string) {
  const [portfolios, setPortfolios] = useState<PortfolioWithHoldings[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()

  const fetchPortfolios = async () => {
    if (!user) return
    
    try {
      setLoading(true)
      let query = supabase
        .from('portfolios')
        .select(`
          *,
          holdings (
            *,
            stock:stocks (*)
          )
        `)
        .order('created_at', { ascending: false })

      if (clientId) {
        query = query.eq('client_id', clientId)
      }

      const { data, error } = await query

      if (error) throw error

      // Calculate P&L for each portfolio
      const portfoliosWithPnL = (data || []).map(portfolio => {
        const totalInvested = portfolio.holdings.reduce((sum: number, holding: any) => 
          sum + (holding.average_cost * holding.quantity), 0)
        const totalCurrentValue = portfolio.holdings.reduce((sum: number, holding: any) => 
          sum + (holding.current_price * holding.quantity), 0)
        const totalPnL = totalCurrentValue - totalInvested
        const totalPnLPercent = totalInvested > 0 ? (totalPnL / totalInvested) * 100 : 0

        return {
          ...portfolio,
          totalPnL,
          totalPnLPercent
        }
      })

      setPortfolios(portfoliosWithPnL)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const createPortfolio = async (portfolioData: Omit<Portfolio, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data, error } = await supabase
        .from('portfolios')
        .insert([portfolioData])
        .select()
        .single()

      if (error) throw error
      
      const newPortfolio = { ...data, holdings: [], totalPnL: 0, totalPnLPercent: 0 }
      setPortfolios(prev => [newPortfolio, ...prev])
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create portfolio')
      return null
    }
  }

  const updatePortfolio = async (id: string, updates: Partial<Portfolio>) => {
    try {
      const { data, error } = await supabase
        .from('portfolios')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      
      setPortfolios(prev => prev.map(portfolio => 
        portfolio.id === id ? { ...portfolio, ...data } : portfolio
      ))
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update portfolio')
      return null
    }
  }

  const deletePortfolio = async (id: string) => {
    try {
      const { error } = await supabase
        .from('portfolios')
        .delete()
        .eq('id', id)

      if (error) throw error
      
      setPortfolios(prev => prev.filter(portfolio => portfolio.id !== id))
      return true
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete portfolio')
      return false
    }
  }

  const addHolding = async (portfolioId: string, holdingData: Omit<Holding, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data, error } = await supabase
        .from('holdings')
        .insert([{ ...holdingData, portfolio_id: portfolioId }])
        .select(`
          *,
          stock:stocks (*)
        `)
        .single()

      if (error) throw error
      
      setPortfolios(prev => prev.map(portfolio => 
        portfolio.id === portfolioId 
          ? { ...portfolio, holdings: [...portfolio.holdings, data] }
          : portfolio
      ))
      
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add holding')
      return null
    }
  }

  const updateHolding = async (id: string, updates: Partial<Holding>) => {
    try {
      const { data, error } = await supabase
        .from('holdings')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select(`
          *,
          stock:stocks (*)
        `)
        .single()

      if (error) throw error
      
      setPortfolios(prev => prev.map(portfolio => ({
        ...portfolio,
        holdings: portfolio.holdings.map(holding => 
          holding.id === id ? data : holding
        )
      })))
      
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update holding')
      return null
    }
  }

  const removeHolding = async (id: string) => {
    try {
      const { error } = await supabase
        .from('holdings')
        .delete()
        .eq('id', id)

      if (error) throw error
      
      setPortfolios(prev => prev.map(portfolio => ({
        ...portfolio,
        holdings: portfolio.holdings.filter(holding => holding.id !== id)
      })))
      
      return true
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove holding')
      return false
    }
  }

  useEffect(() => {
    fetchPortfolios()
  }, [user, clientId])

  return {
    portfolios,
    loading,
    error,
    createPortfolio,
    updatePortfolio,
    deletePortfolio,
    addHolding,
    updateHolding,
    removeHolding,
    refetch: fetchPortfolios
  }
}
