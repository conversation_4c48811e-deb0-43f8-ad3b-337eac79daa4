
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Settings, Key, Database, Bell } from "lucide-react";
import { Switch } from "@/components/ui/switch";

export function SettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Configure your STOCK AIR application preferences</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              API Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="openai-key">OpenAI API Key</Label>
              <Input
                id="openai-key"
                type="password"
                placeholder="sk-..."
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="market-api">Market Data API Key</Label>
              <Input
                id="market-api"
                type="password"
                placeholder="Enter your market data API key"
                className="mt-1"
              />
            </div>
            <Button className="w-full">Save API Keys</Button>
          </CardContent>
        </Card>

        {/* Database Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="auto-backup">Auto Backup</Label>
              <Switch id="auto-backup" />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="data-retention">Data Retention (days)</Label>
              <Input id="data-retention" type="number" defaultValue="365" className="w-20" />
            </div>
            <Button variant="outline" className="w-full">
              Backup Database Now
            </Button>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="price-alerts">Price Alerts</Label>
              <Switch id="price-alerts" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="portfolio-updates">Portfolio Updates</Label>
              <Switch id="portfolio-updates" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="ai-insights">AI Insights</Label>
              <Switch id="ai-insights" />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="email-reports">Email Reports</Label>
              <Switch id="email-reports" />
            </div>
          </CardContent>
        </Card>

        {/* Application Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Application Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="refresh-rate">Data Refresh Rate (minutes)</Label>
              <Input
                id="refresh-rate"
                type="number"
                defaultValue="15"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="default-currency">Default Currency</Label>
              <select className="w-full mt-1 p-2 border rounded-md">
                <option value="INR">INR (₹)</option>
                <option value="USD">USD ($)</option>
              </select>
            </div>
            <Button className="w-full">Update Settings</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
