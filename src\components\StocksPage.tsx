import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  BarChart3, 
  Building2,
  Filter,
  SortAsc,
  SortDesc,
  Eye
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useStocks } from "@/hooks/useStocks";

export function StocksPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [sectorFilter, setSectorFilter] = useState("all");
  const [sortField, setSortField] = useState("symbol");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [viewMode, setViewMode] = useState<"table" | "cards">("table");

  const { stocks, loading } = useStocks();

  // Get unique sectors for filter dropdown
  const sectors = useMemo(() => {
    const uniqueSectors = [...new Set(stocks.map(stock => stock.sector).filter(Boolean))];
    return uniqueSectors.sort();
  }, [stocks]);

  // Filter and sort stocks
  const filteredAndSortedStocks = useMemo(() => {
    let filtered = stocks.filter(stock => {
      const matchesSearch = 
        stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (stock.name && stock.name.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesSector = sectorFilter === "all" || stock.sector === sectorFilter;
      
      return matchesSearch && matchesSector;
    });

    // Sort stocks
    filtered.sort((a, b) => {
      let aValue: any = a[sortField as keyof typeof a];
      let bValue: any = b[sortField as keyof typeof b];

      // Handle null/undefined values
      if (aValue == null) aValue = 0;
      if (bValue == null) bValue = 0;

      // Convert to numbers for numeric fields
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
      }

      // String comparison
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();
      
      if (sortDirection === "asc") {
        return aStr.localeCompare(bStr);
      } else {
        return bStr.localeCompare(aStr);
      }
    });

    return filtered;
  }, [stocks, searchTerm, sectorFilter, sortField, sortDirection]);

  const formatCurrency = (amount: number | null | undefined) => {
    if (amount == null) return "N/A";
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatNumber = (num: number | null | undefined, decimals = 2) => {
    if (num == null) return "N/A";
    return num.toLocaleString('en-IN', { 
      minimumFractionDigits: decimals, 
      maximumFractionDigits: decimals 
    });
  };

  const formatMarketCap = (marketCap: number | null | undefined) => {
    if (marketCap == null) return "N/A";
    
    if (marketCap >= 1e12) {
      return `₹${(marketCap / 1e12).toFixed(2)}T`;
    } else if (marketCap >= 1e9) {
      return `₹${(marketCap / 1e9).toFixed(2)}B`;
    } else if (marketCap >= 1e7) {
      return `₹${(marketCap / 1e7).toFixed(2)}Cr`;
    } else if (marketCap >= 1e5) {
      return `₹${(marketCap / 1e5).toFixed(2)}L`;
    } else {
      return formatCurrency(marketCap);
    }
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading stocks...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
            Stocks Database
          </h1>
          <p className="text-gray-600 mt-2">Comprehensive stock information and analysis</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === "table" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("table")}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            Table
          </Button>
          <Button
            variant={viewMode === "cards" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("cards")}
          >
            <Building2 className="h-4 w-4 mr-2" />
            Cards
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-blue-700 flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Total Stocks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">{stocks.length}</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-emerald-700 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Gainers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-800">
              {stocks.filter(s => (s.changePercent || 0) > 0).length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-red-700 flex items-center gap-2">
              <TrendingDown className="h-4 w-4" />
              Losers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-800">
              {stocks.filter(s => (s.changePercent || 0) < 0).length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-purple-700 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Avg Market Cap
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800">
              {formatMarketCap(
                stocks.reduce((sum, s) => sum + (s.market_cap || 0), 0) / stocks.length
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search stocks by symbol or name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={sectorFilter} onValueChange={setSectorFilter}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by sector" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sectors</SelectItem>
                  {sectors.map(sector => (
                    <SelectItem key={sector} value={sector}>{sector}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-600">
            Showing {filteredAndSortedStocks.length} of {stocks.length} stocks
          </div>
        </CardContent>
      </Card>

      {/* Stocks Display */}
      {filteredAndSortedStocks.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center text-gray-500">
              <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No stocks found</p>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          </CardContent>
        </Card>
      ) : viewMode === "table" ? (
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("symbol")}
                    >
                      <div className="flex items-center gap-2">
                        Symbol {getSortIcon("symbol")}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("name")}
                    >
                      <div className="flex items-center gap-2">
                        Company {getSortIcon("name")}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-right"
                      onClick={() => handleSort("currentPrice")}
                    >
                      <div className="flex items-center justify-end gap-2">
                        Price {getSortIcon("currentPrice")}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-right"
                      onClick={() => handleSort("changePercent")}
                    >
                      <div className="flex items-center justify-end gap-2">
                        Change % {getSortIcon("changePercent")}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-right"
                      onClick={() => handleSort("volume")}
                    >
                      <div className="flex items-center justify-end gap-2">
                        Volume {getSortIcon("volume")}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-right"
                      onClick={() => handleSort("market_cap")}
                    >
                      <div className="flex items-center justify-end gap-2">
                        Market Cap {getSortIcon("market_cap")}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-right"
                      onClick={() => handleSort("pe_ratio")}
                    >
                      <div className="flex items-center justify-end gap-2">
                        P/E {getSortIcon("pe_ratio")}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-right"
                      onClick={() => handleSort("pb_ratio")}
                    >
                      <div className="flex items-center justify-end gap-2">
                        P/B {getSortIcon("pb_ratio")}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort("sector")}
                    >
                      <div className="flex items-center gap-2">
                        Sector {getSortIcon("sector")}
                      </div>
                    </TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAndSortedStocks.map((stock) => (
                    <TableRow key={stock.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium">{stock.symbol}</TableCell>
                      <TableCell className="max-w-48 truncate">{stock.name || "N/A"}</TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(stock.currentPrice)}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className={`flex items-center justify-end gap-1 ${
                          (stock.changePercent || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {(stock.changePercent || 0) >= 0 ?
                            <TrendingUp className="h-3 w-3" /> :
                            <TrendingDown className="h-3 w-3" />
                          }
                          {formatNumber(stock.changePercent, 2)}%
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {formatNumber(stock.volume, 0)}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatMarketCap(stock.market_cap)}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatNumber(stock.pe_ratio, 2)}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatNumber(stock.pb_ratio, 2)}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          {stock.sector || "N/A"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedStocks.map((stock) => (
            <Card key={stock.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{stock.symbol}</CardTitle>
                    <p className="text-sm text-gray-600 truncate">{stock.name || "N/A"}</p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {stock.sector || "N/A"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold">{formatCurrency(stock.currentPrice)}</span>
                    <div className={`flex items-center gap-1 ${
                      (stock.changePercent || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {(stock.changePercent || 0) >= 0 ?
                        <TrendingUp className="h-4 w-4" /> :
                        <TrendingDown className="h-4 w-4" />
                      }
                      <span className="font-medium">{formatNumber(stock.changePercent, 2)}%</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Volume:</span>
                      <span className="font-medium">{formatNumber(stock.volume, 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Market Cap:</span>
                      <span className="font-medium">{formatMarketCap(stock.market_cap)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">P/E:</span>
                      <span className="font-medium">{formatNumber(stock.pe_ratio, 2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">P/B:</span>
                      <span className="font-medium">{formatNumber(stock.pb_ratio, 2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">ROE:</span>
                      <span className="font-medium">{formatNumber(stock.roe, 2)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Dividend:</span>
                      <span className="font-medium">{formatNumber(stock.dividend_yield, 2)}%</span>
                    </div>
                  </div>

                  <Button size="sm" variant="outline" className="w-full">
                    <Eye className="h-3 w-3 mr-2" />
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
