
import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Upload, FileText, Database, Settings, CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/AuthContext";
import { Upload as UploadType } from "@/types/database";

interface UploadProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

export function UploadCenterPage() {
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [uploadHistory, setUploadHistory] = useState<UploadType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();
  const bhavcopyInputRef = useRef<HTMLInputElement>(null);
  const fundamentalsInputRef = useRef<HTMLInputElement>(null);
  const configInputRef = useRef<HTMLInputElement>(null);

  // Fetch upload history on component mount
  useEffect(() => {
    fetchUploadHistory();
  }, [user]);

  const fetchUploadHistory = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('uploads')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      setUploadHistory(data || []);
    } catch (error) {
      console.error('Error fetching upload history:', error);
    }
  };

  const validateFile = (file: File, type: 'bhavcopy' | 'fundamentals' | 'screener_config'): string | null => {
    const maxSize = 50 * 1024 * 1024; // 50MB

    if (file.size > maxSize) {
      return 'File size must be less than 50MB';
    }

    switch (type) {
      case 'bhavcopy':
        if (!file.name.match(/\.(csv|zip)$/i)) {
          return 'Bhavcopy files must be CSV or ZIP format';
        }
        break;
      case 'fundamentals':
        if (!file.name.match(/\.csv$/i)) {
          return 'Fundamentals files must be CSV format';
        }
        break;
      case 'screener_config':
        if (!file.name.match(/\.json$/i)) {
          return 'Config files must be JSON format';
        }
        break;
    }

    return null;
  };

  const processUploadedFile = async (file: File, type: 'bhavcopy' | 'fundamentals' | 'screener_config', uploadId: string, filePath: string) => {
    try {
      let recordsProcessed = 0;

      if (type === 'bhavcopy') {
        recordsProcessed = await processBhavcopyFile(file);
      } else if (type === 'fundamentals') {
        recordsProcessed = await processFundamentalsFile(file);
      } else if (type === 'screener_config') {
        recordsProcessed = await processScreenerConfigFile(file);
      }

      // Update upload status to completed
      await supabase
        .from('uploads')
        .update({
          status: 'completed',
          processed_at: new Date().toISOString(),
          records_processed: recordsProcessed
        })
        .eq('id', uploadId);

      setUploads(prev => prev.map(upload =>
        upload.file === file
          ? { ...upload, status: 'completed' }
          : upload
      ));

      fetchUploadHistory();

      toast({
        title: "Upload completed",
        description: `${file.name} processed successfully. ${recordsProcessed} records imported.`,
      });

    } catch (error) {
      console.error('Processing error:', error);

      await supabase
        .from('uploads')
        .update({
          status: 'failed',
          error_message: error instanceof Error ? error.message : 'Processing failed'
        })
        .eq('id', uploadId);

      setUploads(prev => prev.map(upload =>
        upload.file === file
          ? { ...upload, status: 'error', error: error instanceof Error ? error.message : 'Processing failed' }
          : upload
      ));

      toast({
        title: "Processing failed",
        description: error instanceof Error ? error.message : 'An error occurred during file processing',
        variant: "destructive",
      });
    }
  };

  const processBhavcopyFile = async (file: File): Promise<number> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const text = e.target?.result as string;
          const lines = text.split('\n').filter(line => line.trim());
          const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

          let recordsProcessed = 0;
          const batchSize = 100;

          for (let i = 1; i < lines.length; i += batchSize) {
            const batch = lines.slice(i, i + batchSize);
            const stockData = [];
            const priceData = [];

            for (const line of batch) {
              const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
              if (values.length < headers.length) continue;

              const symbol = values[headers.indexOf('SYMBOL')] || values[0];
              const open = parseFloat(values[headers.indexOf('OPEN')] || values[2]) || 0;
              const high = parseFloat(values[headers.indexOf('HIGH')] || values[3]) || 0;
              const low = parseFloat(values[headers.indexOf('LOW')] || values[4]) || 0;
              const close = parseFloat(values[headers.indexOf('CLOSE')] || values[5]) || 0;
              const volume = parseInt(values[headers.indexOf('TOTTRDQTY')] || values[10]) || 0;

              if (!symbol || close === 0) continue;

              // Upsert stock
              stockData.push({
                symbol: symbol,
                name: symbol, // In real implementation, you'd have a mapping
                exchange: 'NSE',
                sector: 'Unknown',
                industry: 'Unknown',
                market_cap: null,
                listing_date: null
              });

              // Add price data
              priceData.push({
                symbol: symbol,
                date: new Date().toISOString().split('T')[0],
                open: open,
                high: high,
                low: low,
                close: close,
                volume: volume,
                adjusted_close: close
              });
            }

            // Insert stocks (upsert)
            if (stockData.length > 0) {
              await supabase
                .from('stocks')
                .upsert(stockData, { onConflict: 'symbol' });
            }

            // Insert price data
            if (priceData.length > 0) {
              await supabase
                .from('stock_prices')
                .upsert(priceData, { onConflict: 'symbol,date' });
            }

            recordsProcessed += batch.length;
          }

          resolve(recordsProcessed);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const processFundamentalsFile = async (file: File): Promise<number> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const text = e.target?.result as string;
          const lines = text.split('\n').filter(line => line.trim());
          const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

          let recordsProcessed = 0;
          const batchSize = 100;

          for (let i = 1; i < lines.length; i += batchSize) {
            const batch = lines.slice(i, i + batchSize);
            const fundamentalData = [];

            for (const line of batch) {
              const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
              if (values.length < headers.length) continue;

              const symbol = values[headers.indexOf('SYMBOL')] || values[0];
              const year = parseInt(values[headers.indexOf('YEAR')] || values[1]) || new Date().getFullYear();
              const quarter = parseInt(values[headers.indexOf('QUARTER')] || values[2]) || 4;

              if (!symbol) continue;

              fundamentalData.push({
                symbol: symbol,
                year: year,
                quarter: quarter,
                revenue: parseFloat(values[headers.indexOf('REVENUE')] || values[3]) || null,
                net_profit: parseFloat(values[headers.indexOf('NET_PROFIT')] || values[4]) || null,
                eps: parseFloat(values[headers.indexOf('EPS')] || values[5]) || null,
                pe_ratio: parseFloat(values[headers.indexOf('PE_RATIO')] || values[6]) || null,
                pb_ratio: parseFloat(values[headers.indexOf('PB_RATIO')] || values[7]) || null,
                roe: parseFloat(values[headers.indexOf('ROE')] || values[8]) || null,
                debt_to_equity: parseFloat(values[headers.indexOf('DEBT_TO_EQUITY')] || values[9]) || null,
                dividend_yield: parseFloat(values[headers.indexOf('DIVIDEND_YIELD')] || values[10]) || null,
                market_cap: parseFloat(values[headers.indexOf('MARKET_CAP')] || values[11]) || null
              });
            }

            if (fundamentalData.length > 0) {
              await supabase
                .from('stock_fundamentals')
                .upsert(fundamentalData, { onConflict: 'symbol,year,quarter' });
            }

            recordsProcessed += batch.length;
          }

          resolve(recordsProcessed);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const processScreenerConfigFile = async (file: File): Promise<number> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const text = e.target?.result as string;
          const config = JSON.parse(text);

          if (!user) {
            reject(new Error('User not authenticated'));
            return;
          }

          await supabase
            .from('screener_configs')
            .insert({
              user_id: user.id,
              name: config.name || `Imported Config ${Date.now()}`,
              filters: config.filters || []
            });

          resolve(1);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const handleFileUpload = async (file: File, type: 'bhavcopy' | 'fundamentals' | 'screener_config') => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please log in to upload files",
        variant: "destructive",
      });
      return;
    }

    const validationError = validateFile(file, type);
    if (validationError) {
      toast({
        title: "Invalid file",
        description: validationError,
        variant: "destructive",
      });
      return;
    }

    const uploadProgress: UploadProgress = {
      file,
      progress: 0,
      status: 'uploading'
    };

    setUploads(prev => [...prev, uploadProgress]);

    try {
      // Create upload record in database
      const { data: uploadRecord, error: dbError } = await supabase
        .from('uploads')
        .insert({
          user_id: user.id,
          filename: file.name,
          file_type: type,
          file_size: file.size,
          status: 'pending',
          upload_date: new Date().toISOString()
        })
        .select()
        .single();

      if (dbError) throw dbError;

      // Upload file to Supabase Storage
      const filePath = `uploads/${user.id}/${type}/${Date.now()}_${file.name}`;
      const { error: uploadError } = await supabase.storage
        .from('data-uploads')
        .upload(filePath, file, {
          onUploadProgress: (progress) => {
            const percent = (progress.loaded / progress.total) * 100;
            setUploads(prev => prev.map(upload =>
              upload.file === file
                ? { ...upload, progress: percent }
                : upload
            ));
          }
        });

      if (uploadError) throw uploadError;

      // Update upload status
      await supabase
        .from('uploads')
        .update({ status: 'processing' })
        .eq('id', uploadRecord.id);

      setUploads(prev => prev.map(upload =>
        upload.file === file
          ? { ...upload, status: 'processing', progress: 100 }
          : upload
      ));

      // Process the uploaded file
      processUploadedFile(file, type, uploadRecord.id, filePath);

    } catch (error) {
      console.error('Upload error:', error);
      setUploads(prev => prev.map(upload =>
        upload.file === file
          ? { ...upload, status: 'error', error: error instanceof Error ? error.message : 'Upload failed' }
          : upload
      ));

      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : 'An error occurred during upload',
        variant: "destructive",
      });
    }
  };
  const handleUploadClick = (type: 'bhavcopy' | 'fundamentals' | 'screener_config') => {
    const inputRef = type === 'bhavcopy' ? bhavcopyInputRef :
                    type === 'fundamentals' ? fundamentalsInputRef : configInputRef;
    inputRef.current?.click();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>, type: 'bhavcopy' | 'fundamentals' | 'screener_config') => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file, type);
    }
    // Reset input value to allow re-uploading the same file
    event.target.value = '';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-50 border-green-200 text-green-700';
      case 'processing': return 'bg-blue-50 border-blue-200 text-blue-700';
      case 'failed': return 'bg-red-50 border-red-200 text-red-700';
      case 'pending': return 'bg-yellow-50 border-yellow-200 text-yellow-700';
      default: return 'bg-gray-50 border-gray-200 text-gray-700';
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
          Upload Center
        </h1>
        <p className="text-gray-600 mt-2">Manage data uploads for price feeds and fundamentals</p>
      </div>

      {/* Hidden file inputs */}
      <input
        ref={bhavcopyInputRef}
        type="file"
        accept=".csv,.zip"
        onChange={(e) => handleFileSelect(e, 'bhavcopy')}
        className="hidden"
      />
      <input
        ref={fundamentalsInputRef}
        type="file"
        accept=".csv"
        onChange={(e) => handleFileSelect(e, 'fundamentals')}
        className="hidden"
      />
      <input
        ref={configInputRef}
        type="file"
        accept=".json"
        onChange={(e) => handleFileSelect(e, 'screener_config')}
        className="hidden"
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="text-center">
            <Database className="h-12 w-12 mx-auto text-blue-600 mb-2" />
            <CardTitle>NSE Bhavcopy</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">Upload daily NSE price data (CSV/ZIP)</p>
            <Button
              className="w-full"
              onClick={() => handleUploadClick('bhavcopy')}
              disabled={isLoading}
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Bhavcopy
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="text-center">
            <FileText className="h-12 w-12 mx-auto text-emerald-600 mb-2" />
            <CardTitle>Fundamentals Data</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">Upload PE, PB, ROE, EPS data (CSV)</p>
            <Button
              className="w-full"
              onClick={() => handleUploadClick('fundamentals')}
              disabled={isLoading}
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Fundamentals
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="text-center">
            <Settings className="h-12 w-12 mx-auto text-purple-600 mb-2" />
            <CardTitle>Screener Config</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">Upload screener logic (JSON)</p>
            <Button
              className="w-full"
              onClick={() => handleUploadClick('screener_config')}
              disabled={isLoading}
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Config
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Current Uploads Progress */}
      {uploads.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Current Uploads</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {uploads.map((upload, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{upload.file.name}</span>
                    <Badge variant={upload.status === 'completed' ? 'default' : 'secondary'}>
                      {upload.status}
                    </Badge>
                  </div>
                  <Progress value={upload.progress} className="w-full" />
                  {upload.error && (
                    <p className="text-sm text-red-600">{upload.error}</p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload History */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Uploads</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {uploadHistory.length > 0 ? (
              uploadHistory.map((upload) => (
                <div
                  key={upload.id}
                  className={`flex items-center justify-between p-3 rounded-lg border ${getStatusColor(upload.status)}`}
                >
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(upload.status)}
                    <div>
                      <span className="font-medium">{upload.filename}</span>
                      <div className="text-xs opacity-75">
                        {upload.file_type} • {(upload.file_size / 1024 / 1024).toFixed(2)} MB
                        {upload.records_processed && ` • ${upload.records_processed} records`}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm">
                    {upload.status === 'completed' && upload.processed_at &&
                      `Completed ${new Date(upload.processed_at).toLocaleDateString()}`
                    }
                    {upload.status === 'processing' && 'Processing...'}
                    {upload.status === 'failed' && upload.error_message &&
                      `Error: ${upload.error_message}`
                    }
                    {upload.status === 'pending' && 'Pending'}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Upload className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No uploads yet</p>
                <p className="text-sm">Upload your first file to get started</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
