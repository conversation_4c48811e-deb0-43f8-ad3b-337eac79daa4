import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { Stock, StockPrice, StockFundamental, StockWithFundamentals } from '@/types/database'

export function useStocks() {
  const [stocks, setStocks] = useState<StockWithFundamentals[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchStocks = async () => {
    try {
      setLoading(true)
      
      // Fetch stocks with latest prices and fundamentals
      const { data: stocksData, error: stocksError } = await supabase
        .from('stocks')
        .select('*')
        .order('symbol')

      if (stocksError) throw stocksError

      // Fetch latest prices for each stock
      const stocksWithData = await Promise.all(
        (stocksData || []).map(async (stock) => {
          // Get latest price
          const { data: priceData } = await supabase
            .from('stock_prices')
            .select('*')
            .eq('symbol', stock.symbol)
            .order('date', { ascending: false })
            .limit(2)

          // Get latest fundamentals
          const { data: fundamentalData } = await supabase
            .from('stock_fundamentals')
            .select('*')
            .eq('symbol', stock.symbol)
            .order('year', { ascending: false })
            .order('quarter', { ascending: false })
            .limit(1)

          const latestPrice = priceData?.[0]
          const previousPrice = priceData?.[1]
          const fundamentals = fundamentalData?.[0]

          let change = 0
          let changePercent = 0
          
          if (latestPrice && previousPrice) {
            change = latestPrice.close - previousPrice.close
            changePercent = (change / previousPrice.close) * 100
          }

          return {
            ...stock,
            currentPrice: latestPrice?.close,
            change,
            changePercent,
            volume: latestPrice?.volume,
            pe_ratio: fundamentals?.pe_ratio,
            pb_ratio: fundamentals?.pb_ratio,
            roe: fundamentals?.roe,
            eps: fundamentals?.eps,
            dividend_yield: fundamentals?.dividend_yield
          }
        })
      )

      setStocks(stocksWithData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const getStockBySymbol = async (symbol: string): Promise<StockWithFundamentals | null> => {
    try {
      const { data: stockData, error: stockError } = await supabase
        .from('stocks')
        .select('*')
        .eq('symbol', symbol)
        .single()

      if (stockError) throw stockError

      // Get latest price
      const { data: priceData } = await supabase
        .from('stock_prices')
        .select('*')
        .eq('symbol', symbol)
        .order('date', { ascending: false })
        .limit(2)

      // Get latest fundamentals
      const { data: fundamentalData } = await supabase
        .from('stock_fundamentals')
        .select('*')
        .eq('symbol', symbol)
        .order('year', { ascending: false })
        .order('quarter', { ascending: false })
        .limit(1)

      const latestPrice = priceData?.[0]
      const previousPrice = priceData?.[1]
      const fundamentals = fundamentalData?.[0]

      let change = 0
      let changePercent = 0
      
      if (latestPrice && previousPrice) {
        change = latestPrice.close - previousPrice.close
        changePercent = (change / previousPrice.close) * 100
      }

      return {
        ...stockData,
        currentPrice: latestPrice?.close,
        change,
        changePercent,
        volume: latestPrice?.volume,
        pe_ratio: fundamentals?.pe_ratio,
        pb_ratio: fundamentals?.pb_ratio,
        roe: fundamentals?.roe,
        eps: fundamentals?.eps,
        dividend_yield: fundamentals?.dividend_yield
      }
    } catch (err) {
      console.error('Error fetching stock:', err)
      return null
    }
  }

  const getStockPrices = async (symbol: string, days: number = 30): Promise<StockPrice[]> => {
    try {
      const { data, error } = await supabase
        .from('stock_prices')
        .select('*')
        .eq('symbol', symbol)
        .order('date', { ascending: false })
        .limit(days)

      if (error) throw error
      return data || []
    } catch (err) {
      console.error('Error fetching stock prices:', err)
      return []
    }
  }

  const searchStocks = async (query: string): Promise<StockWithFundamentals[]> => {
    try {
      const { data, error } = await supabase
        .from('stocks')
        .select('*')
        .or(`symbol.ilike.%${query}%,name.ilike.%${query}%`)
        .limit(10)

      if (error) throw error

      // Get current prices for search results
      const stocksWithPrices = await Promise.all(
        (data || []).map(async (stock) => {
          const { data: priceData } = await supabase
            .from('stock_prices')
            .select('close, volume')
            .eq('symbol', stock.symbol)
            .order('date', { ascending: false })
            .limit(1)

          const latestPrice = priceData?.[0]

          return {
            ...stock,
            currentPrice: latestPrice?.close,
            volume: latestPrice?.volume
          }
        })
      )

      return stocksWithPrices
    } catch (err) {
      console.error('Error searching stocks:', err)
      return []
    }
  }

  const createStock = async (stockData: Omit<Stock, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data, error } = await supabase
        .from('stocks')
        .insert([stockData])
        .select()
        .single()

      if (error) throw error
      
      const newStock = { ...data, currentPrice: 0, change: 0, changePercent: 0 }
      setStocks(prev => [newStock, ...prev])
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create stock')
      return null
    }
  }

  const updateStock = async (id: string, updates: Partial<Stock>) => {
    try {
      const { data, error } = await supabase
        .from('stocks')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      
      setStocks(prev => prev.map(stock => 
        stock.id === id ? { ...stock, ...data } : stock
      ))
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update stock')
      return null
    }
  }

  useEffect(() => {
    fetchStocks()
  }, [])

  return {
    stocks,
    loading,
    error,
    getStockBySymbol,
    getStockPrices,
    searchStocks,
    createStock,
    updateStock,
    refetch: fetchStocks
  }
}
