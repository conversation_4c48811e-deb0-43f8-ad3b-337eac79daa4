
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Bo<PERSON>, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export function AIAssistantPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">AI Assistant</h1>
        <p className="text-gray-600">Get intelligent insights and analysis for your portfolio</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chat Interface */}
        <div className="lg:col-span-2">
          <Card className="h-[600px] flex flex-col">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5 text-blue-600" />
                Chat with AI Assistant
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              <div className="flex-1 border rounded-lg p-4 mb-4 bg-gray-50">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Bot className="h-6 w-6 text-blue-600 mt-1" />
                    <div className="bg-white p-3 rounded-lg shadow-sm">
                      <p>Hello! I'm your AI assistant. I can help you analyze stocks, generate reports, and provide investment insights. What would you like to know?</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Input placeholder="Ask me about stocks, portfolios, or market analysis..." />
                <Button>
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* AI Capabilities */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">AI Capabilities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800">Stock Analysis</h4>
                <p className="text-sm text-blue-600">Technical & fundamental analysis</p>
              </div>
              <div className="p-3 bg-emerald-50 rounded-lg">
                <h4 className="font-semibold text-emerald-800">Portfolio Insights</h4>
                <p className="text-sm text-emerald-600">Performance and optimization tips</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <h4 className="font-semibold text-purple-800">Market Research</h4>
                <p className="text-sm text-purple-600">Sector trends and opportunities</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <h4 className="font-semibold text-orange-800">Report Generation</h4>
                <p className="text-sm text-orange-600">Automated client reports</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full text-left justify-start">
                Analyze portfolio performance
              </Button>
              <Button variant="outline" className="w-full text-left justify-start">
                Compare sector allocation
              </Button>
              <Button variant="outline" className="w-full text-left justify-start">
                Generate client report
              </Button>
              <Button variant="outline" className="w-full text-left justify-start">
                Screen undervalued stocks
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
