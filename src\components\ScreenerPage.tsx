
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Filter, Play, Save, Download, Plus, X, TrendingUp, TrendingDown, Search } from "lucide-react";
import { useStocks } from "@/hooks/useStocks";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/AuthContext";

interface ScreenerFilter {
  id: string;
  field: string;
  operator: string;
  value: string;
  logic?: 'AND' | 'OR';
}

interface ScreenerConfig {
  id?: string;
  name: string;
  filters: ScreenerFilter[];
  user_id?: string;
  created_at?: string;
}

interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  pe: number;
  pb: number;
  roe: number;
  eps: number;
  sector: string;
  dividend: number;
}

const fields = [
  { value: "currentPrice", label: "Price" },
  { value: "market_cap", label: "Market Cap" },
  { value: "pe_ratio", label: "P/E Ratio" },
  { value: "pb_ratio", label: "P/B Ratio" },
  { value: "roe", label: "ROE %" },
  { value: "eps", label: "EPS" },
  { value: "volume", label: "Volume" },
  { value: "dividend_yield", label: "Dividend Yield" },
  { value: "sector", label: "Sector" },
  { value: "changePercent", label: "Change %" },
];

const operators = [
  { value: "gt", label: "Greater than (>)" },
  { value: "lt", label: "Less than (<)" },
  { value: "eq", label: "Equal to (=)" },
  { value: "gte", label: "Greater than or equal (>=)" },
  { value: "lte", label: "Less than or equal (<=)" },
  { value: "ne", label: "Not equal (!=)" },
  { value: "contains", label: "Contains" },
];

export function ScreenerPage() {
  const [filters, setFilters] = useState<ScreenerFilter[]>([]);
  const [filteredStocks, setFilteredStocks] = useState<StockData[]>([]);
  const [screenerName, setScreenerName] = useState("");
  const [activeTab, setActiveTab] = useState("builder");
  const [savedConfigs, setSavedConfigs] = useState<ScreenerConfig[]>([]);
  const [configName, setConfigName] = useState("");
  const [isSaveConfigOpen, setIsSaveConfigOpen] = useState(false);
  const [isLoadConfigOpen, setIsLoadConfigOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { stocks, loading: stocksLoading } = useStocks();
  const { toast } = useToast();
  const { user } = useAuth();

  // Load saved configs and apply filters when stocks change
  useEffect(() => {
    loadSavedConfigs();
    if (stocks.length > 0) {
      runScreener();
    }
  }, [stocks, filters]);

  const loadSavedConfigs = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('screener_configs')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSavedConfigs(data || []);
    } catch (error) {
      console.error('Error loading saved configs:', error);
    }
  };

  const saveConfig = async () => {
    if (!user || !configName.trim() || filters.length === 0) {
      toast({
        title: "Missing information",
        description: "Please provide a config name and add at least one filter",
        variant: "destructive",
      });
      return;
    }

    try {
      const { data, error } = await supabase
        .from('screener_configs')
        .insert({
          user_id: user.id,
          name: configName,
          filters: filters
        })
        .select()
        .single();

      if (error) throw error;

      setSavedConfigs(prev => [data, ...prev]);
      setConfigName("");
      setIsSaveConfigOpen(false);

      toast({
        title: "Config saved",
        description: `Screener configuration "${configName}" has been saved`,
      });
    } catch (error) {
      toast({
        title: "Save failed",
        description: "Failed to save screener configuration",
        variant: "destructive",
      });
    }
  };

  const loadConfig = (config: ScreenerConfig) => {
    setFilters(config.filters);
    setIsLoadConfigOpen(false);

    toast({
      title: "Config loaded",
      description: `Loaded "${config.name}" configuration`,
    });
  };

  const deleteConfig = async (configId: string) => {
    try {
      const { error } = await supabase
        .from('screener_configs')
        .delete()
        .eq('id', configId);

      if (error) throw error;

      setSavedConfigs(prev => prev.filter(config => config.id !== configId));

      toast({
        title: "Config deleted",
        description: "Screener configuration has been deleted",
      });
    } catch (error) {
      toast({
        title: "Delete failed",
        description: "Failed to delete screener configuration",
        variant: "destructive",
      });
    }
  };

  const addFilter = () => {
    const newFilter: ScreenerFilter = {
      id: Date.now().toString(),
      field: "currentPrice",
      operator: "gt",
      value: "",
      logic: filters.length > 0 ? "AND" : undefined,
    };
    setFilters([...filters, newFilter]);
  };

  const removeFilter = (id: string) => {
    setFilters(filters.filter(f => f.id !== id));
  };

  const updateFilter = (id: string, field: keyof ScreenerFilter, value: string) => {
    setFilters(filters.map(f => f.id === id ? { ...f, [field]: value } : f));
  };

  const runScreener = () => {
    if (stocks.length === 0) {
      setFilteredStocks([]);
      return;
    }

    let results = stocks.map(stock => ({
      symbol: stock.symbol,
      name: stock.name || '',
      price: stock.currentPrice || 0,
      change: stock.change || 0,
      changePercent: stock.changePercent || 0,
      volume: stock.volume || 0,
      marketCap: stock.market_cap || 0,
      pe: stock.pe_ratio || 0,
      pb: stock.pb_ratio || 0,
      roe: stock.roe || 0,
      eps: stock.eps || 0,
      sector: stock.sector || '',
      dividend: stock.dividend_yield || 0
    }));

    filters.forEach(filter => {
      if (!filter.value) return;

      results = results.filter(stock => {
        const stockValue = stock[filter.field as keyof StockData];
        const filterValue = filter.field === "sector" ? filter.value : parseFloat(filter.value);

        switch (filter.operator) {
          case "gt": return Number(stockValue) > Number(filterValue);
          case "lt": return Number(stockValue) < Number(filterValue);
          case "eq": return stockValue === filterValue;
          case "gte": return Number(stockValue) >= Number(filterValue);
          case "lte": return Number(stockValue) <= Number(filterValue);
          case "ne": return stockValue !== filterValue;
          case "contains": return String(stockValue).toLowerCase().includes(String(filterValue).toLowerCase());
          default: return true;
        }
      });
    });

    setFilteredStocks(results);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `₹${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `₹${(num / 1000).toFixed(1)}K`;
    return `₹${num.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
            Advanced Stock Screener
          </h1>
          <p className="text-gray-600 mt-2">Create custom filters to discover investment opportunities</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={runScreener} className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700">
            <Play className="h-4 w-4 mr-2" />
            Run Screener
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Results
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="builder">Filter Builder</TabsTrigger>
          <TabsTrigger value="results">Results ({filteredStocks.length})</TabsTrigger>
          <TabsTrigger value="saved">Saved Screeners</TabsTrigger>
        </TabsList>

        <TabsContent value="builder" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Build Custom Filter
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4 items-center">
                <Input
                  placeholder="Enter screener name..."
                  value={screenerName}
                  onChange={(e) => setScreenerName(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={addFilter} variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Filter
                </Button>
              </div>

              <div className="space-y-3">
                {filters.map((filter, index) => (
                  <div key={filter.id} className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                    {index > 0 && (
                      <Select value={filter.logic} onValueChange={(value) => updateFilter(filter.id, "logic", value)}>
                        <SelectTrigger className="w-20">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="AND">AND</SelectItem>
                          <SelectItem value="OR">OR</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                    
                    <Select value={filter.field} onValueChange={(value) => updateFilter(filter.id, "field", value)}>
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {fields.map(field => (
                          <SelectItem key={field.value} value={field.value}>{field.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select value={filter.operator} onValueChange={(value) => updateFilter(filter.id, "operator", value)}>
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {operators.map(op => (
                          <SelectItem key={op.value} value={op.value}>{op.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Input
                      placeholder="Value"
                      value={filter.value}
                      onChange={(e) => updateFilter(filter.id, "value", e.target.value)}
                      className="w-32"
                    />

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFilter(filter.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>

              {filters.length > 0 && (
                <div className="flex gap-2 pt-4 border-t">
                  <Button onClick={runScreener} className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700">
                    <Play className="h-4 w-4 mr-2" />
                    Run Screener
                  </Button>
                  <Button variant="outline">
                    <Save className="h-4 w-4 mr-2" />
                    Save Filter
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Screener Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-lg border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Symbol</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Change</TableHead>
                      <TableHead>Volume</TableHead>
                      <TableHead>Market Cap</TableHead>
                      <TableHead>P/E</TableHead>
                      <TableHead>P/B</TableHead>
                      <TableHead>ROE</TableHead>
                      <TableHead>Sector</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStocks.map(stock => (
                      <TableRow key={stock.symbol}>
                        <TableCell className="font-medium">{stock.symbol}</TableCell>
                        <TableCell>{formatNumber(stock.price)}</TableCell>
                        <TableCell>
                          <div className={`flex items-center gap-1 ${stock.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {stock.change >= 0 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                            {stock.changePercent.toFixed(2)}%
                          </div>
                        </TableCell>
                        <TableCell>{(stock.volume / 1000).toFixed(0)}K</TableCell>
                        <TableCell>{formatNumber(stock.marketCap * 1000000)}</TableCell>
                        <TableCell>{stock.pe.toFixed(1)}</TableCell>
                        <TableCell>{stock.pb.toFixed(1)}</TableCell>
                        <TableCell>{stock.roe.toFixed(1)}%</TableCell>
                        <TableCell>
                          <Badge variant="secondary">{stock.sector}</Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="saved" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Saved Screeners</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {savedScreeners.map(screener => (
                  <div key={screener.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div>
                      <h3 className="font-medium">{screener.name}</h3>
                      <p className="text-sm text-gray-600">{screener.filters} filters • {screener.results} results</p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">Load</Button>
                      <Button variant="outline" size="sm">
                        <Play className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
