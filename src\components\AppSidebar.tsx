
import {
  LayoutDashboard,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>lter,
  TrendingUp,
  Upload,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Building2
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";

interface AppSidebarProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const menuItems = [
  { id: "dashboard", label: "Dashboard", icon: LayoutDashboard },
  { id: "stocks", label: "Stocks", icon: Building2 },
  { id: "clients", label: "Clients", icon: Users },
  { id: "portfolios", label: "Portfolios", icon: PieChart },
  { id: "screener", label: "Screener", icon: Filter },
  { id: "compare", label: "Compare", icon: TrendingUp },
  { id: "upload", label: "Upload Center", icon: Upload },
  { id: "ai-assistant", label: "AI Assistant", icon: Bo<PERSON> },
  { id: "settings", label: "Settings", icon: Settings },
];

export function AppSidebar({ activeTab, setActiveTab }: AppSidebarProps) {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  return (
    <Sidebar className={isCollapsed ? "w-14" : "w-64"} collapsible="icon">
      <SidebarContent className="bg-gradient-to-b from-slate-900 to-slate-800">
        <div className="p-4 border-b border-slate-700">
          <div className="flex items-center gap-2">
            <LineChart className="h-8 w-8 text-blue-400" />
            {!isCollapsed && (
              <div className="text-white">
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent">
                  STOCK AIR
                </h1>
                <p className="text-xs text-slate-400">Portfolio Manager</p>
              </div>
            )}
          </div>
        </div>

        <SidebarGroup>
          <SidebarGroupLabel className="text-slate-400">Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton
                    onClick={() => setActiveTab(item.id)}
                    className={`${
                      activeTab === item.id
                        ? "bg-gradient-to-r from-blue-600 to-emerald-600 text-white"
                        : "text-slate-300 hover:text-white hover:bg-slate-700"
                    } transition-all duration-200`}
                  >
                    <item.icon className="h-4 w-4" />
                    {!isCollapsed && <span>{item.label}</span>}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
