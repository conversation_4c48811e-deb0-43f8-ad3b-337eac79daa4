
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { TrendingUp, TrendingDown, Plus, X, BarChart3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Legend } from "recharts";

interface Stock {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  marketCap: number;
  pe: number;
  pb: number;
  roe: number;
  eps: number;
  dividend: number;
  sector: string;
  color: string;
}

interface PriceData {
  date: string;
  [key: string]: string | number;
}

const availableStocks: Stock[] = [
  { symbol: "RELIANCE", name: "Reliance Industries", price: 2456.75, change: 24.50, changePercent: 1.01, marketCap: 1665000, pe: 23.4, pb: 2.1, roe: 8.9, eps: 104.8, dividend: 2.1, sector: "Energy", color: "#3b82f6" },
  { symbol: "TCS", name: "Tata Consultancy Services", price: 3245.80, change: -15.20, changePercent: -0.47, marketCap: 1180000, pe: 28.7, pb: 11.2, roe: 39.1, eps: 113.2, dividend: 1.8, sector: "IT", color: "#10b981" },
  { symbol: "INFY", name: "Infosys Limited", price: 1567.45, change: 12.30, changePercent: 0.79, marketCap: 650000, pe: 24.9, pb: 7.8, roe: 31.4, eps: 62.9, dividend: 2.3, sector: "IT", color: "#f59e0b" },
  { symbol: "HDFCBANK", name: "HDFC Bank", price: 1623.90, change: 8.75, changePercent: 0.54, marketCap: 950000, pe: 19.8, pb: 2.4, roe: 12.6, eps: 82.1, dividend: 1.5, sector: "Banking", color: "#ef4444" },
  { symbol: "ICICIBANK", name: "ICICI Bank", price: 1156.20, change: -7.80, changePercent: -0.67, marketCap: 810000, pe: 17.2, pb: 2.8, roe: 16.3, eps: 67.3, dividend: 1.2, sector: "Banking", color: "#8b5cf6" },
  { symbol: "BHARTIARTL", name: "Bharti Airtel", price: 1234.50, change: 18.90, changePercent: 1.55, marketCap: 680000, pe: 35.6, pb: 4.2, roe: 11.8, eps: 34.7, dividend: 1.0, sector: "Telecom", color: "#06b6d4" },
  { symbol: "WIPRO", name: "Wipro Limited", price: 425.30, change: 2.15, changePercent: 0.51, marketCap: 230000, pe: 22.1, pb: 3.1, roe: 14.2, eps: 19.2, dividend: 1.9, sector: "IT", color: "#f97316" },
  { symbol: "MARUTI", name: "Maruti Suzuki", price: 10234.75, change: -45.20, changePercent: -0.44, marketCap: 310000, pe: 28.9, pb: 3.8, roe: 13.1, eps: 354.2, dividend: 1.2, sector: "Auto", color: "#84cc16" },
];

const mockPriceData: PriceData[] = [
  { date: "Jan", RELIANCE: 2200, TCS: 3100, INFY: 1400, HDFCBANK: 1500, ICICIBANK: 1050, BHARTIARTL: 1100 },
  { date: "Feb", RELIANCE: 2250, TCS: 3150, INFY: 1450, HDFCBANK: 1520, ICICIBANK: 1080, BHARTIARTL: 1120 },
  { date: "Mar", RELIANCE: 2300, TCS: 3200, INFY: 1500, HDFCBANK: 1550, ICICIBANK: 1100, BHARTIARTL: 1150 },
  { date: "Apr", RELIANCE: 2350, TCS: 3180, INFY: 1480, HDFCBANK: 1580, ICICIBANK: 1120, BHARTIARTL: 1180 },
  { date: "May", RELIANCE: 2400, TCS: 3220, INFY: 1520, HDFCBANK: 1600, ICICIBANK: 1140, BHARTIARTL: 1200 },
  { date: "Jun", RELIANCE: 2456, TCS: 3245, INFY: 1567, HDFCBANK: 1623, ICICIBANK: 1156, BHARTIARTL: 1234 },
];

// Baseline prices (first data point) for normalization
const baselinePrices: { [key: string]: number } = {
  RELIANCE: 2200,
  TCS: 3100,
  INFY: 1400,
  HDFCBANK: 1500,
  ICICIBANK: 1050,
  BHARTIARTL: 1100,
};

const timeframes = [
  { value: "1M", label: "1 Month" },
  { value: "3M", label: "3 Months" },
  { value: "6M", label: "6 Months" },
  { value: "1Y", label: "1 Year" },
  { value: "5Y", label: "5 Years" },
];

export function ComparePage() {
  const [selectedStocks, setSelectedStocks] = useState<Stock[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTimeframe, setSelectedTimeframe] = useState("6M");
  const [activeTab, setActiveTab] = useState("price");
  const [isNormalized, setIsNormalized] = useState(true);

  const filteredStocks = availableStocks.filter(stock =>
    stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
    stock.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const addStock = (stock: Stock) => {
    if (selectedStocks.length < 5 && !selectedStocks.find(s => s.symbol === stock.symbol)) {
      setSelectedStocks([...selectedStocks, stock]);
    }
  };

  const removeStock = (symbol: string) => {
    setSelectedStocks(selectedStocks.filter(s => s.symbol !== symbol));
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `₹${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `₹${(num / 1000).toFixed(1)}K`;
    return `₹${num.toFixed(2)}`;
  };

  // Transform price data for normalized comparison
  const transformedPriceData = mockPriceData.map(dataPoint => {
    const transformed: PriceData = { date: dataPoint.date };

    selectedStocks.forEach(stock => {
      const currentPrice = dataPoint[stock.symbol] as number;
      const baselinePrice = baselinePrices[stock.symbol];

      if (isNormalized && baselinePrice) {
        // Calculate percentage change from baseline
        transformed[stock.symbol] = ((currentPrice - baselinePrice) / baselinePrice) * 100;
      } else {
        // Use absolute prices
        transformed[stock.symbol] = currentPrice;
      }
    });

    return transformed;
  });

  const fundamentalData = selectedStocks.map(stock => ({
    symbol: stock.symbol,
    PE: stock.pe,
    PB: stock.pb,
    ROE: stock.roe,
    EPS: stock.eps,
    Dividend: stock.dividend,
  }));

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
            Stock Comparison
          </h1>
          <p className="text-gray-600 mt-2">Compare up to 5 stocks side by side</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={isNormalized ? "default" : "outline"}
            size="sm"
            onClick={() => setIsNormalized(!isNormalized)}
            className="mr-4"
          >
            {isNormalized ? "Normalized %" : "Absolute ₹"}
          </Button>
          {timeframes.map(timeframe => (
            <Button
              key={timeframe.value}
              variant={selectedTimeframe === timeframe.value ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedTimeframe(timeframe.value)}
            >
              {timeframe.label}
            </Button>
          ))}
        </div>
      </div>

      <div className="grid md:grid-cols-4 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Add Stocks
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              placeholder="Search stocks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredStocks.map(stock => (
                <div
                  key={stock.symbol}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => addStock(stock)}
                >
                  <div>
                    <div className="font-medium">{stock.symbol}</div>
                    <div className="text-sm text-gray-600">{stock.name}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{formatNumber(stock.price)}</div>
                    <div className={`text-sm ${stock.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {stock.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="md:col-span-3 space-y-6">
          {selectedStocks.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Selected Stocks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {selectedStocks.map(stock => (
                    <Badge
                      key={stock.symbol}
                      variant="secondary"
                      className="flex items-center gap-2 px-3 py-1"
                    >
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: stock.color }}
                      />
                      {stock.symbol}
                      <button
                        onClick={() => removeStock(stock.symbol)}
                        className="ml-1 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {selectedStocks.length > 0 && (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="price">Price Comparison</TabsTrigger>
                <TabsTrigger value="fundamentals">Fundamentals</TabsTrigger>
                <TabsTrigger value="overview">Overview</TabsTrigger>
              </TabsList>

              <TabsContent value="price" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <LineChart className="h-5 w-5" />
                      {isNormalized ? "Normalized Price Comparison (%)" : "Absolute Price Comparison (₹)"}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsLineChart data={transformedPriceData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis
                            label={{
                              value: isNormalized ? 'Change (%)' : 'Price (₹)',
                              angle: -90,
                              position: 'insideLeft'
                            }}
                            tickFormatter={(value) =>
                              isNormalized ? `${value.toFixed(1)}%` : `₹${value}`
                            }
                          />
                          <Tooltip
                            formatter={(value: number, name: string) => [
                              isNormalized
                                ? `${value.toFixed(2)}%`
                                : `₹${value.toLocaleString()}`,
                              name
                            ]}
                            labelFormatter={(label) => `Period: ${label}`}
                          />
                          <Legend />
                          {selectedStocks.map(stock => (
                            <Line
                              key={stock.symbol}
                              type="monotone"
                              dataKey={stock.symbol}
                              stroke={stock.color}
                              strokeWidth={2}
                              dot={{ fill: stock.color, strokeWidth: 2 }}
                            />
                          ))}
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </div>
                    {isNormalized && (
                      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm text-blue-700">
                          <strong>Normalized View:</strong> Shows percentage change from the first data point (Jan) for each stock.
                          This allows fair comparison between stocks with different price ranges.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="fundamentals" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        P/E Ratio Comparison
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={fundamentalData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="symbol" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="PE" fill="#3b82f6" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        ROE Comparison
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={fundamentalData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="symbol" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="ROE" fill="#10b981" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle>Key Fundamentals Comparison</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">Symbol</th>
                            <th className="text-left p-2">P/E Ratio</th>
                            <th className="text-left p-2">P/B Ratio</th>
                            <th className="text-left p-2">ROE %</th>
                            <th className="text-left p-2">EPS</th>
                            <th className="text-left p-2">Dividend %</th>
                          </tr>
                        </thead>
                        <tbody>
                          {selectedStocks.map(stock => (
                            <tr key={stock.symbol} className="border-b">
                              <td className="p-2 font-medium">{stock.symbol}</td>
                              <td className="p-2">{stock.pe.toFixed(1)}</td>
                              <td className="p-2">{stock.pb.toFixed(1)}</td>
                              <td className="p-2">{stock.roe.toFixed(1)}%</td>
                              <td className="p-2">{stock.eps.toFixed(1)}</td>
                              <td className="p-2">{stock.dividend.toFixed(1)}%</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="overview" className="space-y-6">
                <div className="grid gap-6">
                  {selectedStocks.map(stock => (
                    <Card key={stock.symbol}>
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: stock.color }}
                            />
                            <div>
                              <div className="font-bold">{stock.symbol}</div>
                              <div className="text-sm text-gray-600">{stock.name}</div>
                            </div>
                          </div>
                          <Badge variant="secondary">{stock.sector}</Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold">{formatNumber(stock.price)}</div>
                            <div className={`text-sm ${stock.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {stock.change >= 0 ? <TrendingUp className="h-4 w-4 inline mr-1" /> : <TrendingDown className="h-4 w-4 inline mr-1" />}
                              {stock.changePercent.toFixed(2)}%
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold">{formatNumber(stock.marketCap * 1000000)}</div>
                            <div className="text-sm text-gray-600">Market Cap</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold">{stock.pe.toFixed(1)}</div>
                            <div className="text-sm text-gray-600">P/E Ratio</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold">{stock.roe.toFixed(1)}%</div>
                            <div className="text-sm text-gray-600">ROE</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          )}

          {selectedStocks.length === 0 && (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center text-gray-500">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">Select stocks to compare</p>
                  <p>Add up to 5 stocks from the list to start comparison</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
