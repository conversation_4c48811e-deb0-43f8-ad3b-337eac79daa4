export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
        }
        Update: {
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          updated_at?: string
        }
      }
      clients: {
        Row: {
          id: string
          user_id: string
          name: string
          email: string
          phone: string | null
          address: string | null
          risk_tolerance: 'conservative' | 'moderate' | 'aggressive'
          investment_goals: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          name: string
          email: string
          phone?: string | null
          address?: string | null
          risk_tolerance: 'conservative' | 'moderate' | 'aggressive'
          investment_goals?: string | null
        }
        Update: {
          name?: string
          email?: string
          phone?: string | null
          address?: string | null
          risk_tolerance?: 'conservative' | 'moderate' | 'aggressive'
          investment_goals?: string | null
          updated_at?: string
        }
      }
      portfolios: {
        Row: {
          id: string
          client_id: string
          name: string
          description: string | null
          total_value: number
          cash_balance: number
          created_at: string
          updated_at: string
        }
        Insert: {
          client_id: string
          name: string
          description?: string | null
          total_value?: number
          cash_balance?: number
        }
        Update: {
          name?: string
          description?: string | null
          total_value?: number
          cash_balance?: number
          updated_at?: string
        }
      }
      holdings: {
        Row: {
          id: string
          portfolio_id: string
          symbol: string
          quantity: number
          average_cost: number
          current_price: number
          created_at: string
          updated_at: string
        }
        Insert: {
          portfolio_id: string
          symbol: string
          quantity: number
          average_cost: number
          current_price: number
        }
        Update: {
          quantity?: number
          average_cost?: number
          current_price?: number
          updated_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          portfolio_id: string
          symbol: string
          type: 'buy' | 'sell'
          quantity: number
          price: number
          fees: number
          transaction_date: string
          created_at: string
        }
        Insert: {
          portfolio_id: string
          symbol: string
          type: 'buy' | 'sell'
          quantity: number
          price: number
          fees?: number
          transaction_date: string
        }
        Update: {
          quantity?: number
          price?: number
          fees?: number
          transaction_date?: string
        }
      }
      stocks: {
        Row: {
          id: string
          symbol: string
          name: string
          sector: string | null
          industry: string | null
          market_cap: number | null
          listing_date: string | null
          isin: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          symbol: string
          name: string
          sector?: string | null
          industry?: string | null
          market_cap?: number | null
          listing_date?: string | null
          isin?: string | null
        }
        Update: {
          name?: string
          sector?: string | null
          industry?: string | null
          market_cap?: number | null
          listing_date?: string | null
          isin?: string | null
          updated_at?: string
        }
      }
      stock_prices: {
        Row: {
          id: string
          symbol: string
          date: string
          open: number
          high: number
          low: number
          close: number
          volume: number
          adjusted_close: number | null
          created_at: string
        }
        Insert: {
          symbol: string
          date: string
          open: number
          high: number
          low: number
          close: number
          volume: number
          adjusted_close?: number | null
        }
        Update: {
          open?: number
          high?: number
          low?: number
          close?: number
          volume?: number
          adjusted_close?: number | null
        }
      }
      stock_fundamentals: {
        Row: {
          id: string
          symbol: string
          quarter: string
          year: number
          pe_ratio: number | null
          pb_ratio: number | null
          roe: number | null
          eps: number | null
          dividend_yield: number | null
          debt_to_equity: number | null
          current_ratio: number | null
          revenue: number | null
          net_profit: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          symbol: string
          quarter: string
          year: number
          pe_ratio?: number | null
          pb_ratio?: number | null
          roe?: number | null
          eps?: number | null
          dividend_yield?: number | null
          debt_to_equity?: number | null
          current_ratio?: number | null
          revenue?: number | null
          net_profit?: number | null
        }
        Update: {
          pe_ratio?: number | null
          pb_ratio?: number | null
          roe?: number | null
          eps?: number | null
          dividend_yield?: number | null
          debt_to_equity?: number | null
          current_ratio?: number | null
          revenue?: number | null
          net_profit?: number | null
          updated_at?: string
        }
      }
      uploads: {
        Row: {
          id: string
          user_id: string
          filename: string
          file_type: 'bhavcopy' | 'fundamentals' | 'screener_config'
          file_size: number
          status: 'pending' | 'processing' | 'completed' | 'failed'
          records_processed: number | null
          error_message: string | null
          upload_date: string
          processed_at: string | null
          created_at: string
        }
        Insert: {
          user_id: string
          filename: string
          file_type: 'bhavcopy' | 'fundamentals' | 'screener_config'
          file_size: number
          status?: 'pending' | 'processing' | 'completed' | 'failed'
          records_processed?: number | null
          error_message?: string | null
          upload_date: string
          processed_at?: string | null
        }
        Update: {
          status?: 'pending' | 'processing' | 'completed' | 'failed'
          records_processed?: number | null
          error_message?: string | null
          processed_at?: string | null
        }
      }
      screener_configs: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          filters: any // JSON object containing filter configuration
          is_public: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          name: string
          description?: string | null
          filters: any
          is_public?: boolean
        }
        Update: {
          name?: string
          description?: string | null
          filters?: any
          is_public?: boolean
          updated_at?: string
        }
      }
    }
  }
}

export type Profile = Database['public']['Tables']['profiles']['Row']
export type Client = Database['public']['Tables']['clients']['Row']
export type Portfolio = Database['public']['Tables']['portfolios']['Row']
export type Holding = Database['public']['Tables']['holdings']['Row']
export type Transaction = Database['public']['Tables']['transactions']['Row']
export type Stock = Database['public']['Tables']['stocks']['Row']
export type StockPrice = Database['public']['Tables']['stock_prices']['Row']
export type StockFundamental = Database['public']['Tables']['stock_fundamentals']['Row']
export type Upload = Database['public']['Tables']['uploads']['Row']
export type ScreenerConfig = Database['public']['Tables']['screener_configs']['Row']

// Additional interfaces for enhanced functionality
export interface StockWithPrice extends Stock {
  currentPrice?: number
  change?: number
  changePercent?: number
  volume?: number
}

export interface StockWithFundamentals extends StockWithPrice {
  pe_ratio?: number
  pb_ratio?: number
  roe?: number
  eps?: number
  dividend_yield?: number
}

export interface PortfolioWithHoldings extends Portfolio {
  holdings: (Holding & { stock: Stock })[]
  totalPnL: number
  totalPnLPercent: number
}

export interface ClientWithPortfolios extends Client {
  portfolios: PortfolioWithHoldings[]
  totalValue: number
}