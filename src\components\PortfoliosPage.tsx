
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import {
  TrendingUp,
  TrendingDown,
  Plus,
  Edit,
  Trash2,
  PlusCircle,
  Briefcase,
  DollarSign
} from "lucide-react";
import { usePortfolios } from "@/hooks/usePortfolios";
import { useStocks } from "@/hooks/useStocks";
import { useClients } from "@/hooks/useClients";
import { useToast } from "@/hooks/use-toast";

export function PortfoliosPage() {
  const [selectedPortfolio, setSelectedPortfolio] = useState<string | null>(null);
  const [isCreatePortfolioOpen, setIsCreatePortfolioOpen] = useState(false);
  const [isAddHoldingOpen, setIsAddHoldingOpen] = useState(false);
  const [newPortfolioName, setNewPortfolioName] = useState("");
  const [newPortfolioDescription, setNewPortfolioDescription] = useState("");
  const [selectedClientId, setSelectedClientId] = useState("");
  const [newHoldingSymbol, setNewHoldingSymbol] = useState("");
  const [newHoldingQuantity, setNewHoldingQuantity] = useState("");
  const [newHoldingAvgCost, setNewHoldingAvgCost] = useState("");
  const [stockSearchResults, setStockSearchResults] = useState<any[]>([]);

  const { portfolios, loading, createPortfolio, deletePortfolio, addHolding, removeHolding } = usePortfolios();
  const { searchStocks, getStockBySymbol } = useStocks();
  const { clients } = useClients();
  const { toast } = useToast();

  const selectedPortfolioData = portfolios.find(p => p.id === selectedPortfolio);

  // Calculate totals for selected portfolio or all portfolios
  const calculateTotals = () => {
    const targetPortfolios = selectedPortfolio
      ? portfolios.filter(p => p.id === selectedPortfolio)
      : portfolios;

    const totalInvested = targetPortfolios.reduce((sum, portfolio) =>
      sum + portfolio.holdings.reduce((holdingSum: number, holding: any) =>
        holdingSum + (holding.average_cost * holding.quantity), 0), 0);

    const totalCurrentValue = targetPortfolios.reduce((sum, portfolio) =>
      sum + portfolio.holdings.reduce((holdingSum: number, holding: any) =>
        holdingSum + (holding.current_price * holding.quantity), 0), 0);

    const totalPnL = totalCurrentValue - totalInvested;
    const totalPnLPercent = totalInvested > 0 ? (totalPnL / totalInvested) * 100 : 0;

    return { totalInvested, totalCurrentValue, totalPnL, totalPnLPercent };
  };

  const { totalInvested, totalCurrentValue, totalPnL, totalPnLPercent } = calculateTotals();

  const handleCreatePortfolio = async () => {
    if (!newPortfolioName.trim() || !selectedClientId) {
      toast({
        title: "Missing information",
        description: "Please provide portfolio name and select a client",
        variant: "destructive",
      });
      return;
    }

    const result = await createPortfolio({
      client_id: selectedClientId,
      name: newPortfolioName,
      description: newPortfolioDescription,
      total_value: 0,
      cash_balance: 0
    });

    if (result) {
      setNewPortfolioName("");
      setNewPortfolioDescription("");
      setSelectedClientId("");
      setIsCreatePortfolioOpen(false);
      toast({
        title: "Portfolio created",
        description: `${newPortfolioName} has been created successfully`,
      });
    }
  };

  const handleAddHolding = async () => {
    if (!selectedPortfolio || !newHoldingSymbol || !newHoldingQuantity || !newHoldingAvgCost) {
      toast({
        title: "Missing information",
        description: "Please fill in all holding details",
        variant: "destructive",
      });
      return;
    }

    const stock = await getStockBySymbol(newHoldingSymbol);
    if (!stock) {
      toast({
        title: "Stock not found",
        description: "Please select a valid stock symbol",
        variant: "destructive",
      });
      return;
    }

    const result = await addHolding(selectedPortfolio, {
      symbol: newHoldingSymbol,
      quantity: parseInt(newHoldingQuantity),
      average_cost: parseFloat(newHoldingAvgCost),
      current_price: stock.currentPrice || parseFloat(newHoldingAvgCost)
    });

    if (result) {
      setNewHoldingSymbol("");
      setNewHoldingQuantity("");
      setNewHoldingAvgCost("");
      setIsAddHoldingOpen(false);
      toast({
        title: "Holding added",
        description: `${newHoldingSymbol} has been added to the portfolio`,
      });
    }
  };

  const handleStockSearch = async (query: string) => {
    if (query.length > 2) {
      const results = await searchStocks(query);
      setStockSearchResults(results);
    } else {
      setStockSearchResults([]);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading portfolios...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
            Portfolio Management
          </h1>
          <p className="text-gray-600 mt-2">Track and manage your investment portfolios</p>
        </div>
        <Dialog open={isCreatePortfolioOpen} onOpenChange={setIsCreatePortfolioOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700">
              <Plus className="h-4 w-4 mr-2" />
              Create Portfolio
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Portfolio</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="client">Client</Label>
                <Select value={selectedClientId} onValueChange={setSelectedClientId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a client" />
                  </SelectTrigger>
                  <SelectContent>
                    {clients.map(client => (
                      <SelectItem key={client.id} value={client.id}>
                        {client.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="name">Portfolio Name</Label>
                <Input
                  id="name"
                  value={newPortfolioName}
                  onChange={(e) => setNewPortfolioName(e.target.value)}
                  placeholder="Enter portfolio name"
                />
              </div>
              <div>
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  value={newPortfolioDescription}
                  onChange={(e) => setNewPortfolioDescription(e.target.value)}
                  placeholder="Enter portfolio description"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreatePortfolioOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreatePortfolio}>
                  Create Portfolio
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Portfolio Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="h-5 w-5" />
            Portfolio Selection
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-center">
            <Select value={selectedPortfolio || "all"} onValueChange={(value) => setSelectedPortfolio(value === "all" ? null : value)}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Select portfolio" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Portfolios</SelectItem>
                {portfolios.map(portfolio => (
                  <SelectItem key={portfolio.id} value={portfolio.id}>
                    {portfolio.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedPortfolio && (
              <Dialog open={isAddHoldingOpen} onOpenChange={setIsAddHoldingOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add Holding
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Holding</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="symbol">Stock Symbol</Label>
                      <Input
                        id="symbol"
                        value={newHoldingSymbol}
                        onChange={(e) => {
                          setNewHoldingSymbol(e.target.value);
                          handleStockSearch(e.target.value);
                        }}
                        placeholder="Enter stock symbol"
                      />
                      {stockSearchResults.length > 0 && (
                        <div className="mt-2 border rounded-md max-h-32 overflow-y-auto">
                          {stockSearchResults.map(stock => (
                            <div
                              key={stock.symbol}
                              className="p-2 hover:bg-gray-50 cursor-pointer"
                              onClick={() => {
                                setNewHoldingSymbol(stock.symbol);
                                setStockSearchResults([]);
                              }}
                            >
                              <div className="font-medium">{stock.symbol}</div>
                              <div className="text-sm text-gray-600">{stock.name}</div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="quantity">Quantity</Label>
                      <Input
                        id="quantity"
                        type="number"
                        value={newHoldingQuantity}
                        onChange={(e) => setNewHoldingQuantity(e.target.value)}
                        placeholder="Enter quantity"
                      />
                    </div>
                    <div>
                      <Label htmlFor="avgCost">Average Cost</Label>
                      <Input
                        id="avgCost"
                        type="number"
                        step="0.01"
                        value={newHoldingAvgCost}
                        onChange={(e) => setNewHoldingAvgCost(e.target.value)}
                        placeholder="Enter average cost"
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsAddHoldingOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleAddHolding}>
                        Add Holding
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Portfolio Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-blue-700 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Total Invested
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">{formatCurrency(totalInvested)}</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-emerald-700">Current Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-800">{formatCurrency(totalCurrentValue)}</div>
          </CardContent>
        </Card>

        <Card className={`bg-gradient-to-br ${totalPnL >= 0 ? 'from-green-50 to-green-100' : 'from-red-50 to-red-100'}`}>
          <CardHeader className="pb-2">
            <CardTitle className={`text-sm ${totalPnL >= 0 ? 'text-green-700' : 'text-red-700'}`}>
              Total P&L
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold flex items-center ${totalPnL >= 0 ? 'text-green-800' : 'text-red-800'}`}>
              {totalPnL >= 0 ? <TrendingUp className="h-5 w-5 mr-1" /> : <TrendingDown className="h-5 w-5 mr-1" />}
              {formatCurrency(Math.abs(totalPnL))}
            </div>
            <p className={`text-sm ${totalPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {totalPnL >= 0 ? '+' : ''}{totalPnLPercent.toFixed(2)}%
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-purple-700">Portfolios</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800">{portfolios.length}</div>
            <p className="text-sm text-purple-600">Active portfolios</p>
          </CardContent>
        </Card>
      </div>

      {/* Holdings Display */}
      {portfolios.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center text-gray-500">
              <Briefcase className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No portfolios found</p>
              <p>Create your first portfolio to get started</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="holdings" className="space-y-6">
          <TabsList>
            <TabsTrigger value="holdings">Holdings</TabsTrigger>
            <TabsTrigger value="portfolios">Portfolio Overview</TabsTrigger>
          </TabsList>

          <TabsContent value="holdings">
            <Card>
              <CardHeader>
                <CardTitle>
                  {selectedPortfolio
                    ? `${selectedPortfolioData?.name} - Holdings`
                    : "All Holdings"
                  }
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3">Symbol</th>
                        <th className="text-left p-3">Portfolio</th>
                        <th className="text-left p-3">Quantity</th>
                        <th className="text-right p-3">Avg Price</th>
                        <th className="text-right p-3">Current Price</th>
                        <th className="text-right p-3">Total Value</th>
                        <th className="text-right p-3">P&L</th>
                        <th className="text-right p-3">P&L %</th>
                        <th className="text-right p-3">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(selectedPortfolio ? [selectedPortfolioData] : portfolios)
                        .filter(Boolean)
                        .flatMap(portfolio =>
                          portfolio!.holdings.map((holding: any) => {
                            const totalValue = holding.current_price * holding.quantity;
                            const invested = holding.average_cost * holding.quantity;
                            const pnl = totalValue - invested;
                            const pnlPercent = invested > 0 ? (pnl / invested) * 100 : 0;

                            return (
                              <tr key={`${portfolio!.id}-${holding.id}`} className="border-b hover:bg-gray-50">
                                <td className="p-3">
                                  <div>
                                    <div className="font-semibold">{holding.symbol}</div>
                                    <div className="text-sm text-gray-600">{holding.stock?.name}</div>
                                  </div>
                                </td>
                                <td className="p-3">
                                  <Badge variant="outline">{portfolio!.name}</Badge>
                                </td>
                                <td className="p-3">{holding.quantity}</td>
                                <td className="p-3 text-right">{formatCurrency(holding.average_cost)}</td>
                                <td className="p-3 text-right">{formatCurrency(holding.current_price)}</td>
                                <td className="p-3 text-right font-semibold">{formatCurrency(totalValue)}</td>
                                <td className={`p-3 text-right font-semibold ${pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {pnl >= 0 ? '+' : ''}{formatCurrency(pnl)}
                                </td>
                                <td className="p-3 text-right">
                                  <Badge variant={pnl >= 0 ? "default" : "destructive"} className="bg-opacity-20">
                                    {pnl >= 0 ? '+' : ''}{pnlPercent.toFixed(2)}%
                                  </Badge>
                                </td>
                                <td className="p-3 text-right">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeHolding(holding.id)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </td>
                              </tr>
                            );
                          })
                        )}
                    </tbody>
                  </table>
                  {(selectedPortfolio ? [selectedPortfolioData] : portfolios)
                    .filter(Boolean)
                    .every(portfolio => portfolio!.holdings.length === 0) && (
                    <div className="text-center py-8 text-gray-500">
                      <PlusCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No holdings found</p>
                      <p className="text-sm">Add stocks to your portfolio to get started</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="portfolios">
            <div className="grid gap-6">
              {portfolios.map(portfolio => (
                <Card key={portfolio.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>{portfolio.name}</CardTitle>
                        {portfolio.description && (
                          <p className="text-sm text-gray-600 mt-1">{portfolio.description}</p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedPortfolio(portfolio.id)}
                        >
                          View Details
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deletePortfolio(portfolio.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-lg font-semibold">{portfolio.holdings.length}</div>
                        <div className="text-sm text-gray-600">Holdings</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold">{formatCurrency(portfolio.totalPnL)}</div>
                        <div className="text-sm text-gray-600">P&L</div>
                      </div>
                      <div className="text-center">
                        <div className={`text-lg font-semibold ${portfolio.totalPnLPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {portfolio.totalPnLPercent >= 0 ? '+' : ''}{portfolio.totalPnLPercent.toFixed(2)}%
                        </div>
                        <div className="text-sm text-gray-600">Returns</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold">{formatCurrency(portfolio.total_value)}</div>
                        <div className="text-sm text-gray-600">Total Value</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
